const express = require('express');
const { uploadMusic, uploadCover, uploadAvatar } = require('../middleware/upload');
const { getPresignedUrl, BUCKETS } = require('../config/minio');

const router = express.Router();

// Test music upload endpoint
router.post('/music', uploadMusic, async (req, res) => {
  try {
    const uploadResult = req.uploadResult;
    
    // Generate a presigned URL for accessing the uploaded file
    const presignedUrl = await getPresignedUrl(
      uploadResult.bucket, 
      uploadResult.objectName, 
      24 * 60 * 60 // 24 hours
    );
    
    res.status(200).json({
      success: true,
      message: 'Music file uploaded successfully',
      data: {
        ...uploadResult,
        accessUrl: presignedUrl
      }
    });
  } catch (error) {
    console.error('Music upload error:', error);
    res.status(500).json({
      error: 'Upload processing failed',
      message: error.message
    });
  }
});

// Test cover image upload endpoint
router.post('/cover', uploadCover, async (req, res) => {
  try {
    const uploadResult = req.uploadResult;
    
    // For public images, we can generate a direct URL
    const imageUrl = `http://localhost:9000/${uploadResult.bucket}/${uploadResult.objectName}`;
    
    res.status(200).json({
      success: true,
      message: 'Cover image uploaded successfully',
      data: {
        ...uploadResult,
        imageUrl: imageUrl
      }
    });
  } catch (error) {
    console.error('Cover upload error:', error);
    res.status(500).json({
      error: 'Upload processing failed',
      message: error.message
    });
  }
});

// Test avatar upload endpoint
router.post('/avatar', uploadAvatar, async (req, res) => {
  try {
    const uploadResult = req.uploadResult;
    
    // For public avatars, we can generate a direct URL
    const avatarUrl = `http://localhost:9000/${uploadResult.bucket}/${uploadResult.objectName}`;
    
    res.status(200).json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: {
        ...uploadResult,
        avatarUrl: avatarUrl
      }
    });
  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({
      error: 'Upload processing failed',
      message: error.message
    });
  }
});

// Get upload status and limits
router.get('/info', (req, res) => {
  res.json({
    maxFileSize: process.env.MAX_FILE_SIZE || '50MB',
    allowedAudioFormats: (process.env.ALLOWED_AUDIO_FORMATS || 'mp3,flac,wav,aac').split(','),
    allowedImageFormats: (process.env.ALLOWED_IMAGE_FORMATS || 'jpg,jpeg,png,webp').split(','),
    buckets: {
      music: BUCKETS.MUSIC,
      images: BUCKETS.IMAGES,
      avatars: BUCKETS.AVATARS
    },
    endpoints: {
      music: '/api/v1/upload/music',
      cover: '/api/v1/upload/cover',
      avatar: '/api/v1/upload/avatar'
    }
  });
});

module.exports = router;

const express = require('express');
const { uploadMusic, uploadMultipleMusic, getSupportedFormats } = require('../controllers/musicUploadController');
const { uploadMusic: uploadMusicMiddleware } = require('../middleware/upload');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 获取支持的音频格式信息（公开接口）
router.get('/formats', getSupportedFormats);

// 单个音乐文件上传（需要认证）
router.post('/upload', authenticateToken, uploadMusicMiddleware, uploadMusic);

// 批量音乐文件上传（需要认证）
// 注意：批量上传需要特殊的multer配置，暂时使用单个文件上传的中间件
router.post('/upload/batch', authenticateToken, uploadMusicMiddleware, uploadMultipleMusic);

module.exports = router;

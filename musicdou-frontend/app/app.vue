<template>
  <div class="min-h-screen bg-white dark:bg-slate-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <NuxtRouteAnnouncer />

    <!-- 主要内容区域 -->
    <div class="flex flex-col min-h-screen">
      <!-- 导航栏 -->
      <header class="bg-white dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700 transition-colors duration-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center">
              <h1 class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                🎵 MusicDou
              </h1>
            </div>

            <!-- 主题切换按钮 -->
            <div class="flex items-center space-x-4">
              <button
                @click="toggleTheme"
                class="p-2 rounded-lg bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 transition-colors duration-200"
                :title="themeName"
              >
                <span class="w-5 h-5 flex items-center justify-center">
                  {{ colorMode.value === 'dark' ? '🌙' : '☀️' }}
                </span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="flex-1">
        <NuxtPage />
      </main>

      <!-- 页脚 -->
      <footer class="bg-white dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700 transition-colors duration-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div class="text-center text-gray-600 dark:text-gray-400">
            <p>&copy; 2025 MusicDou. 现代化音乐分享平台</p>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
// 主题管理
const colorMode = useColorMode()

const toggleTheme = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

const themeName = computed(() => {
  switch (colorMode.value) {
    case 'dark':
      return '深色模式'
    case 'light':
      return '浅色模式'
    default:
      return '跟随系统'
  }
})

const themeIcon = computed(() => {
  switch (colorMode.value) {
    case 'dark':
      return 'moon'
    case 'light':
      return 'sun'
    default:
      return 'computer-desktop'
  }
})

// 页面元数据
useHead({
  title: 'MusicDou - 现代化音乐分享平台',
  meta: [
    { name: 'description', content: '发现、分享和享受音乐的最佳平台' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})

// 初始化认证状态
// TODO: 后续实现认证逻辑
</script>

import type { User, LoginCredentials, RegisterData, AuthResponse } from '~/types'

export const useAuth = () => {
  const { api } = useApi()
  const { showError, showSuccess } = useNotification()

  // 状态管理
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  const isLoading = ref(false)
  const token = useCookie<string | null>('auth-token', {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7天
    secure: true,
    sameSite: 'strict'
  })

  // JWT Token管理
  const setToken = (newToken: string) => {
    token.value = newToken
  }

  const clearToken = () => {
    token.value = null
  }

  // 获取用户信息
  const fetchUser = async () => {
    if (!token.value) return null

    try {
      const response = await api('/auth/me')
      user.value = response.data
      return response.data
    } catch (error) {
      // Token可能已过期，清除本地存储
      clearToken()
      user.value = null
      throw error
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials) => {
    isLoading.value = true
    try {
      const response: AuthResponse = await api('/auth/login', {
        method: 'POST',
        body: credentials
      })

      if (response.success) {
        setToken(response.data.token)
        user.value = response.data.user
        showSuccess('登录成功！')

        // 跳转到首页或之前访问的页面
        const redirect = useRoute().query.redirect as string
        await navigateTo(redirect || '/')

        return response.data
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error: any) {
      showError(error.message || '登录失败，请检查用户名和密码')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterData) => {
    isLoading.value = true
    try {
      const response: AuthResponse = await api('/auth/register', {
        method: 'POST',
        body: userData
      })

      if (response.success) {
        setToken(response.data.token)
        user.value = response.data.user
        showSuccess('注册成功！欢迎加入MusicDou！')

        await navigateTo('/')
        return response.data
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error: any) {
      showError(error.message || '注册失败，请稍后重试')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    isLoading.value = true
    try {
      if (token.value) {
        await api('/auth/logout', {
          method: 'POST'
        })
      }
    } catch (error) {
      console.warn('登出请求失败:', error)
    } finally {
      clearToken()
      user.value = null
      isLoading.value = false
      showSuccess('已安全退出')
      await navigateTo('/login')
    }
  }

  // 刷新Token
  const refreshToken = async () => {
    if (!token.value) return false

    try {
      const response: AuthResponse = await api('/auth/refresh', {
        method: 'POST'
      })

      if (response.success) {
        setToken(response.data.token)
        return true
      }
      return false
    } catch (error) {
      clearToken()
      user.value = null
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        await fetchUser()
      } catch (error) {
        console.warn('初始化认证失败:', error)
      }
    }
  }

  // 检查认证状态
  const checkAuth = () => {
    return !!token.value && !!user.value
  }

  return {
    user: readonly(user),
    isLoggedIn,
    isLoading: readonly(isLoading),
    token: readonly(token),
    login,
    register,
    logout,
    fetchUser,
    refreshToken,
    initAuth,
    checkAuth,
    setToken,
    clearToken
  }
}

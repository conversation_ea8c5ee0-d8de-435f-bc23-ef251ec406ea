import type { ApiResponse } from '~/types'

export const useApi = () => {
  const config = useRuntimeConfig()
  const { $fetch } = useNuxtApp()
  const { handleApiError } = useErrorHandler()
  const notification = useNotification()

  // 创建API实例
  const api = $fetch.create({
    baseURL: config.public.apiBase,
    onRequest({ request, options }) {
      // 添加认证token
      const token = useCookie('auth-token')
      if (token.value) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token.value}`
        }
      }

      // 添加请求ID用于追踪
      options.headers = {
        ...options.headers,
        'X-Request-ID': Math.random().toString(36).substr(2, 9)
      }
    },
    onResponseError({ response, request }) {
      // 处理认证错误
      if (response.status === 401) {
        // 清除token并跳转到登录页
        const token = useCookie('auth-token')
        token.value = null
        notification.error('登录已过期', '请重新登录')
        navigateTo('/login')
        return
      }

      // 处理其他错误
      const error = handleApiError({ response, request })

      // 对于某些错误类型，显示通知
      if (response.status >= 500) {
        notification.error('服务器错误', '请稍后重试')
      } else if (response.status === 403) {
        notification.error('权限不足', '您没有执行此操作的权限')
      } else if (response.status === 404) {
        notification.error('资源不存在', '请求的资源未找到')
      }
    }
  })

  // 通用API请求方法
  const request = async <T = any>(
    url: string,
    options: any = {}
  ): Promise<ApiResponse<T>> => {
    try {
      const response = await api<ApiResponse<T>>(url, options)
      return response
    } catch (error: any) {
      console.error('API请求错误:', error)
      throw error
    }
  }

  // GET请求
  const get = <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'GET',
      params
    })
  }

  // POST请求
  const post = <T = any>(url: string, body?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'POST',
      body
    })
  }

  // PUT请求
  const put = <T = any>(url: string, body?: any): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'PUT',
      body
    })
  }

  // DELETE请求
  const del = <T = any>(url: string): Promise<ApiResponse<T>> => {
    return request<T>(url, {
      method: 'DELETE'
    })
  }

  // 文件上传
  const upload = async <T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> => {
    const formData = new FormData()
    formData.append('file', file)

    return request<T>(url, {
      method: 'POST',
      body: formData,
      onUploadProgress: (progress: any) => {
        if (onProgress && progress.total) {
          const percentage = Math.round((progress.loaded * 100) / progress.total)
          onProgress(percentage)
        }
      }
    })
  }

  return {
    api,
    request,
    get,
    post,
    put,
    delete: del,
    upload
  }
}

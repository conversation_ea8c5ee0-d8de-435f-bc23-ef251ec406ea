<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
              发现音乐
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
              探索个性化推荐和热门内容
            </p>
          </div>
          
          <!-- 快速搜索 -->
          <div class="flex items-center space-x-4">
            <NuxtLink
              to="/search"
              class="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 
                     text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600
                     transition-colors duration-200"
            >
              <Icon name="MagnifyingGlassIcon" class="w-4 h-4" />
              <span>搜索</span>
            </NuxtLink>
            
            <!-- 设置按钮 -->
            <button
              @click="showSettings = true"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                     rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              title="推荐设置"
            >
              <Icon name="Cog6ToothIcon" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 加载状态 -->
      <div v-if="isInitialLoading" class="flex justify-center py-12">
        <Loading size="xl" />
      </div>

      <!-- 主要内容 -->
      <div v-else class="space-y-8">
        <!-- 个性化推荐 -->
        <RecommendationSection
          v-if="recommendationStore.forYou.length > 0"
          title="为你推荐"
          description="基于你的喜好和听歌历史"
          icon="HeartIcon"
          :items="recommendationStore.forYou"
          type="music"
          layout="horizontal"
          :is-loading="recommendationStore.isLoading"
          @refresh="refreshForYou"
          @view-all="viewAllForYou"
          @play="handlePlayMusic"
          @like="handleLikeMusic"
          @add-to-playlist="handleAddToPlaylist"
        />

        <!-- 新发布 -->
        <RecommendationSection
          v-if="recommendationStore.newReleases.length > 0"
          title="新发布"
          description="最新上线的音乐作品"
          icon="SparklesIcon"
          :items="recommendationStore.newReleases"
          type="music"
          layout="horizontal"
          :is-loading="recommendationStore.isLoading"
          @refresh="refreshNewReleases"
          @view-all="viewAllNewReleases"
          @play="handlePlayMusic"
          @like="handleLikeMusic"
          @add-to-playlist="handleAddToPlaylist"
        />

        <!-- 热门音乐 -->
        <RecommendationSection
          v-if="recommendationStore.trending.length > 0"
          title="热门音乐"
          description="当前最受欢迎的音乐"
          icon="FireIcon"
          :items="recommendationStore.trending"
          type="music"
          layout="horizontal"
          :is-loading="recommendationStore.isLoading"
          @refresh="refreshTrending"
          @view-all="viewAllTrending"
          @play="handlePlayMusic"
          @like="handleLikeMusic"
          @add-to-playlist="handleAddToPlaylist"
        />

        <!-- 推荐歌单 -->
        <RecommendationSection
          v-if="recommendationStore.recommendedPlaylists.length > 0"
          title="推荐歌单"
          description="精选歌单推荐"
          icon="QueueListIcon"
          :items="recommendationStore.recommendedPlaylists"
          type="playlist"
          layout="grid"
          :grid-cols="4"
          :is-loading="recommendationStore.isLoading"
          @refresh="refreshRecommendedPlaylists"
          @view-all="viewAllRecommendedPlaylists"
          @play="handlePlayPlaylist"
          @like="handleLikePlaylist"
        />

        <!-- 推荐分类 -->
        <div v-if="recommendationStore.categories.length > 0" class="space-y-6">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              音乐分类
            </h2>
            <NuxtLink
              to="/discover/categories"
              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 
                     font-medium transition-colors duration-200"
            >
              查看全部分类
            </NuxtLink>
          </div>
          
          <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <NuxtLink
              v-for="category in recommendationStore.categories.slice(0, 12)"
              :key="category.id"
              :to="`/discover/categories/${category.id}`"
              class="group relative aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-primary-400 to-primary-600 
                     hover:scale-105 transition-transform duration-200"
            >
              <img
                v-if="category.coverUrl"
                :src="category.coverUrl"
                :alt="category.name"
                class="absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-200"></div>
              <div class="absolute inset-0 flex items-center justify-center">
                <h3 class="text-white font-semibold text-center px-2">
                  {{ category.name }}
                </h3>
              </div>
            </NuxtLink>
          </div>
        </div>

        <!-- 相似艺术家 -->
        <RecommendationSection
          v-if="recommendationStore.similarArtists.length > 0"
          title="你可能喜欢的艺术家"
          description="基于你的音乐品味推荐"
          icon="UserGroupIcon"
          :items="recommendationStore.similarArtists"
          type="user"
          layout="horizontal"
          :is-loading="recommendationStore.isLoading"
          @refresh="refreshSimilarArtists"
          @view-all="viewAllSimilarArtists"
          @follow="handleFollowUser"
        />

        <!-- 心情推荐 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <Icon name="FaceSmileIcon" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
              <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  心情音乐
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  根据心情选择音乐
                </p>
              </div>
            </div>
          </div>
          
          <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-3">
            <button
              v-for="mood in moods"
              :key="mood.id"
              @click="handleMoodSelect(mood.id)"
              class="flex flex-col items-center p-4 rounded-lg border border-gray-200 dark:border-gray-700
                     hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-primary-300 dark:hover:border-primary-600
                     transition-all duration-200 group"
            >
              <div :class="['w-12 h-12 rounded-full flex items-center justify-center mb-2', mood.bgColor]">
                <Icon :name="mood.icon" :class="['w-6 h-6', mood.textColor]" />
              </div>
              <span class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400">
                {{ mood.name }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 推荐设置模态框 -->
    <RecommendationSettings
      v-if="showSettings"
      @close="showSettings = false"
      @settings-updated="handleSettingsUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import type { Music, Playlist, User } from '~/types'

// 页面元数据
definePageMeta({
  title: '发现',
  description: '发现新音乐和个性化推荐'
})

// 状态管理
const recommendationStore = useRecommendationStore()
const { recordUserAction } = useRecommendationApi()

// 响应式数据
const isInitialLoading = ref(true)
const showSettings = ref(false)

// 心情选项
const moods = [
  { id: 'happy', name: '开心', icon: 'FaceSmileIcon', bgColor: 'bg-yellow-100 dark:bg-yellow-900', textColor: 'text-yellow-600 dark:text-yellow-400' },
  { id: 'sad', name: '伤感', icon: 'FaceFrownIcon', bgColor: 'bg-blue-100 dark:bg-blue-900', textColor: 'text-blue-600 dark:text-blue-400' },
  { id: 'energetic', name: '活力', icon: 'BoltIcon', bgColor: 'bg-orange-100 dark:bg-orange-900', textColor: 'text-orange-600 dark:text-orange-400' },
  { id: 'calm', name: '平静', icon: 'CloudIcon', bgColor: 'bg-green-100 dark:bg-green-900', textColor: 'text-green-600 dark:text-green-400' },
  { id: 'romantic', name: '浪漫', icon: 'HeartIcon', bgColor: 'bg-pink-100 dark:bg-pink-900', textColor: 'text-pink-600 dark:text-pink-400' },
  { id: 'focus', name: '专注', icon: 'EyeIcon', bgColor: 'bg-purple-100 dark:bg-purple-900', textColor: 'text-purple-600 dark:text-purple-400' }
]

// 初始化
onMounted(async () => {
  try {
    // 初始化推荐设置
    recommendationStore.initializeSettings()
    
    // 加载推荐数据
    await recommendationStore.initializeRecommendations()
    
    // 加载推荐歌单
    await recommendationStore.loadRecommendedPlaylists()
    
    // 加载相似艺术家
    await recommendationStore.loadSimilarArtists()
    
    // 开始自动刷新
    recommendationStore.startAutoRefresh()
  } catch (error) {
    console.error('初始化发现页面失败:', error)
  } finally {
    isInitialLoading.value = false
  }
})

// 组件卸载时停止自动刷新
onUnmounted(() => {
  recommendationStore.stopAutoRefresh()
})

// 刷新方法
const refreshForYou = () => {
  recommendationStore.loadForYouRecommendations()
}

const refreshNewReleases = () => {
  recommendationStore.loadNewReleases()
}

const refreshTrending = () => {
  recommendationStore.loadTrendingMusic()
}

const refreshRecommendedPlaylists = () => {
  recommendationStore.loadRecommendedPlaylists()
}

const refreshSimilarArtists = () => {
  recommendationStore.loadSimilarArtists()
}

// 查看全部方法
const viewAllForYou = () => {
  navigateTo('/discover/for-you')
}

const viewAllNewReleases = () => {
  navigateTo('/discover/new-releases')
}

const viewAllTrending = () => {
  navigateTo('/discover/trending')
}

const viewAllRecommendedPlaylists = () => {
  navigateTo('/discover/playlists')
}

const viewAllSimilarArtists = () => {
  navigateTo('/discover/artists')
}

// 事件处理
const handlePlayMusic = async (music: Music) => {
  // 记录播放行为
  await recordUserAction({
    type: 'play',
    musicId: music.id,
    context: 'discover'
  })
  
  // 这里集成音乐播放器
  console.log('播放音乐:', music)
}

const handleLikeMusic = async (music: Music) => {
  // 记录点赞行为
  await recordUserAction({
    type: 'like',
    musicId: music.id,
    context: 'discover'
  })
  
  // 这里处理点赞逻辑
  console.log('点赞音乐:', music)
}

const handleAddToPlaylist = (music: Music) => {
  // 这里打开添加到歌单的模态框
  console.log('添加到歌单:', music)
}

const handlePlayPlaylist = async (playlist: Playlist) => {
  // 记录播放行为
  await recordUserAction({
    type: 'play',
    playlistId: playlist.id,
    context: 'discover'
  })
  
  // 这里处理歌单播放
  console.log('播放歌单:', playlist)
}

const handleLikePlaylist = async (playlist: Playlist) => {
  // 记录点赞行为
  await recordUserAction({
    type: 'like',
    playlistId: playlist.id,
    context: 'discover'
  })
  
  // 这里处理歌单点赞
  console.log('点赞歌单:', playlist)
}

const handleFollowUser = (user: User) => {
  // 这里处理关注用户
  console.log('关注用户:', user)
}

const handleMoodSelect = (moodId: string) => {
  // 跳转到心情推荐页面
  navigateTo(`/discover/mood/${moodId}`)
}

const handleSettingsUpdated = () => {
  // 设置更新后刷新推荐
  recommendationStore.refreshRecommendations(true)
}

// SEO优化
useHead({
  title: '发现音乐 - MusicDou',
  meta: [
    {
      name: 'description',
      content: '在MusicDou发现新音乐，获取个性化推荐，探索热门内容和精选歌单'
    }
  ]
})
</script>

<style scoped>
/* 分类卡片渐变背景 */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 心情按钮悬停效果 */
.group:hover .group-hover\:text-primary-600 {
  color: rgb(37 99 235);
}

.dark .group:hover .group-hover\:text-primary-400 {
  color: rgb(96 165 250);
}

/* 平滑过渡 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .sm\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>

<template>
  <div class="min-h-screen bg-white dark:bg-slate-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <!-- 未登录状态：显示登录/注册界面 -->
    <div v-if="!isLoggedIn" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
      <div class="max-w-md w-full mx-4">
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl p-8">
          <!-- Logo和标题 -->
          <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              🎵 MusicDou
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
              发现你的音乐世界
            </p>
          </div>

          <!-- 登录/注册切换 -->
          <div class="flex mb-6 bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
            <button
              @click="authMode = 'login'"
              :class="[
                'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
                authMode === 'login'
                  ? 'bg-white dark:bg-slate-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              登录
            </button>
            <button
              @click="authMode = 'register'"
              :class="[
                'flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors',
                authMode === 'register'
                  ? 'bg-white dark:bg-slate-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              ]"
            >
              注册
            </button>
          </div>

          <!-- 登录表单 -->
          <form v-if="authMode === 'login'" @submit.prevent="handleLogin" class="space-y-4">
            <div>
              <input
                v-model="loginForm.email"
                type="email"
                placeholder="邮箱地址"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div>
              <input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div v-if="loginError" class="text-red-600 text-sm">
              {{ loginError }}
            </div>
            <button
              type="submit"
              :disabled="isLoading"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ isLoading ? '登录中...' : '登录' }}
            </button>
          </form>

          <!-- 注册表单 -->
          <form v-else @submit.prevent="handleRegister" class="space-y-4">
            <div>
              <input
                v-model="registerForm.username"
                type="text"
                placeholder="用户名"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div>
              <input
                v-model="registerForm.email"
                type="email"
                placeholder="邮箱地址"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div>
              <input
                v-model="registerForm.password"
                type="password"
                placeholder="密码"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div>
              <input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="确认密码"
                required
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
              />
            </div>
            <div class="flex items-center">
              <input
                id="agree-terms"
                v-model="registerForm.agreeToTerms"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                required
              />
              <label for="agree-terms" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                我同意服务条款和隐私政策
              </label>
            </div>
            <div v-if="registerError" class="text-red-600 text-sm">
              {{ registerError }}
            </div>
            <button
              type="submit"
              :disabled="isLoading || !registerForm.agreeToTerms"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ isLoading ? '注册中...' : '注册' }}
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- 已登录状态：显示欢迎页面 -->
    <div v-else class="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-500 to-blue-600">
      <div class="max-w-md w-full mx-4">
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-xl p-8 text-center">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            欢迎回来！
          </h1>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            {{ user?.username || '用户' }}，开始你的音乐之旅吧
          </p>
          <div class="space-y-4">
            <button class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
              开始探索音乐
            </button>
            <button
              @click="handleLogout"
              class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 认证状态管理
const isLoggedIn = ref(false)
const isLoading = ref(false)
const user = ref(null)

// 表单状态
const authMode = ref<'login' | 'register'>('login')
const loginError = ref('')
const registerError = ref('')

// 登录表单
const loginForm = reactive({
  email: '',
  password: ''
})

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

// 处理登录
const handleLogin = async () => {
  loginError.value = ''
  isLoading.value = true

  try {
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟成功登录
    user.value = { username: loginForm.email.split('@')[0] }
    isLoggedIn.value = true

    // 清空表单
    loginForm.email = ''
    loginForm.password = ''
  } catch (error: any) {
    loginError.value = error.message || '登录失败'
  } finally {
    isLoading.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  registerError.value = ''

  // 验证密码确认
  if (registerForm.password !== registerForm.confirmPassword) {
    registerError.value = '两次输入的密码不一致'
    return
  }

  isLoading.value = true

  try {
    // 模拟注册请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟成功注册并登录
    user.value = { username: registerForm.username }
    isLoggedIn.value = true

    // 清空表单
    Object.assign(registerForm, {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      agreeToTerms: false
    })
  } catch (error: any) {
    registerError.value = error.message || '注册失败'
  } finally {
    isLoading.value = false
  }
}

// 处理退出登录
const handleLogout = () => {
  user.value = null
  isLoggedIn.value = false
  authMode.value = 'login'
}

// 页面元数据
useHead({
  title: 'MusicDou - 发现你的音乐世界',
  meta: [
    { name: 'description', content: '在MusicDou，分享、发现和享受无限音乐。现代化的音乐分享平台，为音乐爱好者量身打造。' }
  ]
})
</script>

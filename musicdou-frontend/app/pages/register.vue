<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/public" class="flex items-center justify-center space-x-2 mb-6">
          <Icon name="MusicalNoteIcon" class="w-10 h-10 text-primary-500" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">
            MusicDou
          </span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
          创建账户
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          加入 MusicDou，开始你的音乐之旅
        </p>
      </div>

      <!-- Register Form -->
      <Card class="mt-8">
        <form class="space-y-6" @submit.prevent="handleRegister">
          <!-- Username -->
          <Input
            v-model="form.username"
            type="text"
            label="用户名"
            placeholder="请输入用户名"
            left-icon="UserIcon"
            required
            :error="errors.username"
            @blur="validateUsername"
          />

          <!-- Email -->
          <Input
            v-model="form.email"
            type="email"
            label="邮箱地址"
            placeholder="请输入邮箱地址"
            left-icon="EnvelopeIcon"
            required
            :error="errors.email"
            @blur="validateEmail"
          />

          <!-- Password -->
          <div>
            <Input
              v-model="form.password"
              type="password"
              label="密码"
              placeholder="请输入密码"
              left-icon="LockClosedIcon"
              required
              :error="errors.password"
              @blur="validatePassword"
            />
            <!-- Password Strength Indicator -->
            <div v-if="form.password" class="mt-2">
              <div class="flex items-center space-x-2">
                <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    :class="passwordStrengthClasses"
                    class="h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${passwordStrength.percentage}%` }"
                  />
                </div>
                <span :class="passwordStrengthTextClasses" class="text-xs font-medium">
                  {{ passwordStrength.text }}
                </span>
              </div>
            </div>
          </div>

          <!-- Confirm Password -->
          <Input
            v-model="form.confirmPassword"
            type="password"
            label="确认密码"
            placeholder="请再次输入密码"
            left-icon="LockClosedIcon"
            required
            :error="errors.confirmPassword"
            @blur="validateConfirmPassword"
          />

          <!-- Terms Agreement -->
          <div class="flex items-start">
            <input
              v-model="form.agreeTerms"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-1"
              required
            />
            <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              我同意
              <NuxtLink to="/terms" class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                服务条款
              </NuxtLink>
              和
              <NuxtLink to="/privacy" class="text-primary-600 hover:text-primary-500 dark:text-primary-400">
                隐私政策
              </NuxtLink>
            </label>
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            :loading="isLoading"
            :disabled="!isFormValid"
            full-width
            size="lg"
          >
            {{ isLoading ? '注册中...' : '创建账户' }}
          </Button>

          <!-- Error Message -->
          <div v-if="registerError" class="text-center">
            <p class="text-sm text-red-600 dark:text-red-400">
              {{ registerError }}
            </p>
          </div>
        </form>
      </Card>

      <!-- Social Register -->
      <Card class="mt-6">
        <div class="space-y-4">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                或者使用
              </span>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              @click="handleSocialRegister('google')"
            >
              <Icon name="google" class="w-5 h-5 mr-2" />
              Google
            </Button>
            <Button
              variant="outline"
              @click="handleSocialRegister('github')"
            >
              <Icon name="github" class="w-5 h-5 mr-2" />
              GitHub
            </Button>
          </div>
        </div>
      </Card>

      <!-- Login Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          已有账户？
          <NuxtLink
            to="/login"
            class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400"
          >
            立即登录
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '注册',
  layout: false,
  middleware: 'guest'
})

// 表单数据
const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

// 错误状态
const errors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 加载状态
const isLoading = ref(false)
const registerError = ref('')

// 认证相关
const { register } = useAuth()
const router = useRouter()

// 表单验证
const validateUsername = () => {
  if (!form.username) {
    errors.username = '请输入用户名'
  } else if (form.username.length < 3) {
    errors.username = '用户名至少需要3个字符'
  } else if (!/^[a-zA-Z0-9_]+$/.test(form.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
  } else {
    errors.username = ''
  }
}

const validateEmail = () => {
  if (!form.email) {
    errors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = ''
  }
}

const validatePassword = () => {
  if (!form.password) {
    errors.password = '请输入密码'
  } else if (form.password.length < 8) {
    errors.password = '密码至少需要8个字符'
  } else {
    errors.password = ''
  }
}

const validateConfirmPassword = () => {
  if (!form.confirmPassword) {
    errors.confirmPassword = '请确认密码'
  } else if (form.password !== form.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 密码强度检查
const passwordStrength = computed(() => {
  const password = form.password
  if (!password) return { percentage: 0, text: '', level: 0 }

  let score = 0
  let feedback = []

  // 长度检查
  if (password.length >= 8) score += 25
  else feedback.push('至少8个字符')

  // 包含小写字母
  if (/[a-z]/.test(password)) score += 25
  else feedback.push('包含小写字母')

  // 包含大写字母
  if (/[A-Z]/.test(password)) score += 25
  else feedback.push('包含大写字母')

  // 包含数字或特殊字符
  if (/[\d\W]/.test(password)) score += 25
  else feedback.push('包含数字或特殊字符')

  let level = 0
  let text = ''
  
  if (score < 50) {
    level = 1
    text = '弱'
  } else if (score < 75) {
    level = 2
    text = '中等'
  } else if (score < 100) {
    level = 3
    text = '强'
  } else {
    level = 4
    text = '很强'
  }

  return { percentage: score, text, level }
})

const passwordStrengthClasses = computed(() => {
  const level = passwordStrength.value.level
  if (level === 1) return 'bg-red-500'
  if (level === 2) return 'bg-yellow-500'
  if (level === 3) return 'bg-blue-500'
  if (level === 4) return 'bg-green-500'
  return 'bg-gray-300'
})

const passwordStrengthTextClasses = computed(() => {
  const level = passwordStrength.value.level
  if (level === 1) return 'text-red-500'
  if (level === 2) return 'text-yellow-500'
  if (level === 3) return 'text-blue-500'
  if (level === 4) return 'text-green-500'
  return 'text-gray-500'
})

// 表单有效性
const isFormValid = computed(() => {
  return form.username && 
         form.email && 
         form.password && 
         form.confirmPassword &&
         form.agreeTerms &&
         !errors.username && 
         !errors.email && 
         !errors.password && 
         !errors.confirmPassword
})

// 注册处理
const handleRegister = async () => {
  // 验证表单
  validateUsername()
  validateEmail()
  validatePassword()
  validateConfirmPassword()
  
  if (!isFormValid.value) {
    return
  }

  isLoading.value = true
  registerError.value = ''

  try {
    await register({
      username: form.username,
      email: form.email,
      password: form.password
    })

    // 注册成功，重定向到登录页面
    await router.push('/login?message=注册成功，请登录')
  } catch (error: any) {
    registerError.value = error.message || '注册失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 社交注册
const handleSocialRegister = async (provider: string) => {
  try {
    // 这里实现社交注册逻辑
    console.log(`Social register with ${provider}`)
  } catch (error: any) {
    registerError.value = error.message || `${provider} 注册失败`
  }
}

// 页面标题
useHead({
  title: '注册 - MusicDou',
  meta: [
    { name: 'description', content: '注册 MusicDou 账户，开始你的音乐之旅' }
  ]
})
</script>

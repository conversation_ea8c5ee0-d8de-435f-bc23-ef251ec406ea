<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <UiLoading size="xl" />
    </div>

    <!-- 歌单详情 -->
    <div v-else-if="playlist" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 歌单头部信息 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
        <div class="flex flex-col md:flex-row gap-6">
          <!-- 歌单封面 -->
          <div class="flex-shrink-0">
            <img
              :src="playlist.coverUrl || '/default-playlist-cover.jpg'"
              :alt="playlist.name"
              class="w-48 h-48 rounded-lg object-cover shadow-lg"
            />
          </div>

          <!-- 歌单信息 -->
          <div class="flex-1">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {{ playlist.name }}
                </h1>
                <p v-if="playlist.description" class="text-gray-600 dark:text-gray-400 mb-4">
                  {{ playlist.description }}
                </p>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center gap-2">
                <UiButton
                  v-if="isOwner"
                  @click="handleEdit"
                  variant="outline"
                  size="sm"
                >
                  <UiIcon name="pencil" class="w-4 h-4" />
                  编辑
                </UiButton>

                <UiButton
                  @click="handleLike"
                  :variant="playlist.isLiked ? 'primary' : 'outline'"
                  size="sm"
                >
                  <UiIcon name="heart" class="w-4 h-4" />
                  {{ playlist.likeCount }}
                </UiButton>

                <SocialShareButton
                  target-type="playlist"
                  :target-id="playlistId"
                  :target-data="playlist"
                  variant="outline"
                  size="sm"
                  @shared="handlePlaylistShared"
                />
              </div>
            </div>

            <!-- 歌单统计 -->
            <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-4">
              <span>{{ playlist.songCount }} 首歌曲</span>
              <span>{{ formatDuration(playlist.duration) }}</span>
              <span>{{ playlist.playCount }} 次播放</span>
              <span>创建于 {{ formatDate(playlist.createdAt) }}</span>
            </div>

            <!-- 创建者信息 -->
            <div v-if="playlist.user" class="flex items-center gap-3">
              <img
                :src="playlist.user.avatar || '/default-avatar.jpg'"
                :alt="playlist.user.username"
                class="w-8 h-8 rounded-full"
              />
              <span class="text-sm text-gray-600 dark:text-gray-400">
                创建者: {{ playlist.user.username }}
              </span>
            </div>

            <!-- 播放按钮 -->
            <div class="flex items-center gap-4 mt-6">
              <UiButton
                @click="handlePlayAll"
                variant="primary"
                size="lg"
                :disabled="!playlist.songs?.length"
                class="flex items-center gap-2"
              >
                <UiIcon name="play" class="w-5 h-5" />
                播放全部
              </UiButton>

              <UiButton
                @click="handleShuffle"
                variant="outline"
                size="lg"
                :disabled="!playlist.songs?.length"
                class="flex items-center gap-2"
              >
                <UiIcon name="arrow-path" class="w-5 h-5" />
                随机播放
              </UiButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 歌曲列表 -->
      <SongManager
        :songs="playlist.songs || []"
        :can-edit="isOwner"
        :current-song="currentSong"
        :is-playing="isPlaying"
        @play-song="handlePlaySong"
        @play-all="handlePlayAll"
        @remove-song="handleRemoveSong"
        @reorder-songs="handleReorderSongs"
        @like-song="handleLikeSong"
        @add-to-queue="handleAddToQueue"
        @add-to-playlist="handleAddToPlaylist"
        @add-songs="showAddSongModal = true"
      />

      <!-- 评论区域 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mt-8">
        <SocialCommentList
          target-type="playlist"
          :target-id="playlistId"
        />
      </div>
    </div>

    <!-- 404 状态 -->
    <div v-else class="flex flex-col items-center justify-center min-h-screen">
      <UiIcon name="exclamation-triangle" class="w-16 h-16 text-gray-400 mb-4" />
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        歌单不存在
      </h2>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        您访问的歌单可能已被删除或不存在
      </p>
      <UiButton @click="$router.push('/playlists')" variant="primary">
        返回歌单列表
      </UiButton>
    </div>

    <!-- 编辑歌单模态框 -->
    <PlaylistForm
      v-if="showEditModal"
      :playlist="playlist"
      @close="showEditModal = false"
      @success="handleEditSuccess"
    />

    <!-- 添加歌曲模态框 -->
    <AddSongModal
      v-if="showAddSongModal"
      :playlist-id="playlist?.id"
      @close="showAddSongModal = false"
      @success="handleAddSongSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import type { Playlist, Music } from '~/types'

// 页面元数据
definePageMeta({
  middleware: 'auth'
})

// 路由参数
const route = useRoute()
const playlistId = route.params.id as string

// 响应式数据
const playlist = ref<Playlist | null>(null)
const loading = ref(true)
const showEditModal = ref(false)
const showAddSongModal = ref(false)
const currentSong = ref<Music | null>(null)
const isPlaying = ref(false)

// Composables
const { getPlaylistById, reorderPlaylistSongs } = usePlaylistApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()
const { user } = useAuth()
const playerStore = usePlayerStore()

// 计算属性
const isOwner = computed(() => {
  return user.value && playlist.value && user.value.id === playlist.value.userId
})

// 获取歌单详情
const fetchPlaylist = async () => {
  try {
    loading.value = true
    const response = await getPlaylistById(playlistId)
    playlist.value = response.data
  } catch (error) {
    handleError(error, '获取歌单详情失败')
    playlist.value = null
  } finally {
    loading.value = false
  }
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 编辑歌单
const handleEdit = () => {
  showEditModal.value = true
}

// 点赞/取消点赞
const handleLike = async () => {
  if (!playlist.value) return

  try {
    const socialStore = useSocialStore()
    await socialStore.toggleLike('playlist', playlist.value.id)

    // 更新本地状态
    playlist.value.isLiked = !playlist.value.isLiked
    if (playlist.value.isLiked) {
      playlist.value.likeCount++
    } else {
      playlist.value.likeCount = Math.max(0, playlist.value.likeCount - 1)
    }

    showNotification(
      playlist.value.isLiked ? '已添加到收藏' : '已取消收藏',
      'success'
    )
  } catch (error) {
    handleError(error, '操作失败')
  }
}

// 处理歌单分享
const handlePlaylistShared = (platform: string) => {
  console.log('Playlist shared to:', platform)
}

// 播放全部
const handlePlayAll = (mode: 'order' | 'shuffle' = 'order') => {
  if (!playlist.value?.songs?.length) return

  // 设置播放队列
  playerStore.setQueue(playlist.value.songs, 0, mode === 'shuffle')

  showNotification(
    mode === 'shuffle'
      ? `随机播放歌单: ${playlist.value.name}`
      : `开始播放歌单: ${playlist.value.name}`,
    'info'
  )
}

// 随机播放
const handleShuffle = () => {
  handlePlayAll('shuffle')
}

// 播放单曲
const handlePlaySong = (song: Music, index: number) => {
  if (!playlist.value?.songs) return

  // 设置播放队列并播放指定歌曲
  playerStore.setQueue(playlist.value.songs, index)
  currentSong.value = song
  isPlaying.value = true

  showNotification(`播放: ${song.title} - ${song.artist}`, 'info')
}

// 移除歌曲
const handleRemoveSong = async (song: Music) => {
  if (!playlist.value || !confirm(`确定要从歌单中移除"${song.title}"吗？`)) {
    return
  }

  try {
    const { removeSongFromPlaylist } = usePlaylistApi()
    await removeSongFromPlaylist(playlist.value.id, song.id)

    // 更新本地数据
    if (playlist.value.songs) {
      playlist.value.songs = playlist.value.songs.filter(s => s.id !== song.id)
      playlist.value.songCount--
    }

    showNotification('歌曲已移除', 'success')
  } catch (error) {
    handleError(error, '移除歌曲失败')
  }
}

// 编辑成功处理
const handleEditSuccess = () => {
  showEditModal.value = false
  fetchPlaylist()
}

// 重排歌曲
const handleReorderSongs = async (songs: Music[]) => {
  if (!playlist.value) return

  try {
    const songIds = songs.map(song => song.id)
    await reorderPlaylistSongs(playlist.value.id, songIds)

    // 更新本地数据
    playlist.value.songs = songs

    showNotification('歌曲顺序已更新', 'success')
  } catch (error) {
    handleError(error, '重排歌曲失败')
    // 恢复原始顺序
    await fetchPlaylist()
  }
}

// 喜欢歌曲
const handleLikeSong = async (song: Music) => {
  try {
    // TODO: 实现歌曲点赞功能
    song.isLiked = !song.isLiked
    song.likeCount += song.isLiked ? 1 : -1

    showNotification(
      song.isLiked ? '已添加到我喜欢的音乐' : '已从我喜欢的音乐中移除',
      'success'
    )
  } catch (error) {
    handleError(error, '操作失败')
  }
}

// 添加到播放队列
const handleAddToQueue = (song: Music) => {
  playerStore.addToQueue(song)
  showNotification(`已添加"${song.title}"到播放队列`, 'success')
}

// 添加到其他歌单
const handleAddToPlaylist = (song: Music) => {
  // TODO: 实现添加到其他歌单的功能
  showNotification('功能开发中...', 'info')
}

// 添加歌曲成功处理
const handleAddSongSuccess = () => {
  showAddSongModal.value = false
  fetchPlaylist()
}

// 页面加载时获取数据
onMounted(() => {
  fetchPlaylist()
})

// 设置页面标题
watchEffect(() => {
  if (playlist.value) {
    useHead({
      title: `${playlist.value.name} - 歌单详情`
    })
  }
})
</script>

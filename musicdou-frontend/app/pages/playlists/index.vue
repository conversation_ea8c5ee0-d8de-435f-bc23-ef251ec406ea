<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              我的歌单
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
              管理和浏览您的音乐收藏
            </p>
          </div>
          <UiButton
            @click="showCreateModal = true"
            variant="primary"
            size="lg"
            class="flex items-center gap-2"
          >
            <UiIcon name="plus" class="w-5 h-5" />
            创建歌单
          </UiButton>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col sm:flex-row gap-4 mb-6">
        <!-- 搜索框 -->
        <div class="flex-1">
          <UiInput
            v-model="searchQuery"
            placeholder="搜索歌单..."
            type="search"
            icon="magnifying-glass"
            @input="handleSearch"
          />
        </div>
        
        <!-- 排序选择 -->
        <div class="flex gap-2">
          <select
            v-model="sortBy"
            @change="handleSort"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="createdAt">创建时间</option>
            <option value="updatedAt">更新时间</option>
            <option value="name">名称</option>
            <option value="songCount">歌曲数量</option>
            <option value="playCount">播放次数</option>
          </select>
          
          <button
            @click="toggleSortOrder"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <UiIcon :name="sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'" class="w-4 h-4" />
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center py-12">
        <UiLoading size="lg" />
      </div>

      <!-- 歌单网格 -->
      <div v-else-if="playlists.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <PlaylistCard
          v-for="playlist in playlists"
          :key="playlist.id"
          :playlist="playlist"
          @edit="handleEdit"
          @delete="handleDelete"
          @play="handlePlay"
        />
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <UiIcon name="musical-note" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {{ searchQuery ? '没有找到匹配的歌单' : '还没有歌单' }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ searchQuery ? '尝试使用不同的关键词搜索' : '创建您的第一个歌单，开始收集喜爱的音乐' }}
        </p>
        <UiButton
          v-if="!searchQuery"
          @click="showCreateModal = true"
          variant="primary"
        >
          创建歌单
        </UiButton>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="flex justify-center mt-8">
        <nav class="flex items-center gap-2">
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
          >
            上一页
          </button>
          
          <span class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
          >
            下一页
          </button>
        </nav>
      </div>
    </div>

    <!-- 创建歌单模态框 -->
    <PlaylistForm
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑歌单模态框 -->
    <PlaylistForm
      v-if="showEditModal && editingPlaylist"
      :playlist="editingPlaylist"
      @close="showEditModal = false"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import type { Playlist } from '~/types'

// 页面元数据
definePageMeta({
  middleware: 'auth',
  title: '我的歌单'
})

// 响应式数据
const playlists = ref<Playlist[]>([])
const loading = ref(true)
const searchQuery = ref('')
const sortBy = ref('createdAt')
const sortOrder = ref<'asc' | 'desc'>('desc')
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 12

// 模态框状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingPlaylist = ref<Playlist | null>(null)

// Composables
const { getUserPlaylists } = usePlaylistApi()
const { showNotification } = useNotification()
const { handleError } = useErrorHandler()

// 获取歌单列表
const fetchPlaylists = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize,
      search: searchQuery.value || undefined,
      sortBy: sortBy.value === 'createdAt' ? 'latest' :
             sortBy.value === 'playCount' ? 'popular' :
             sortBy.value
    }

    const response = await getUserPlaylists('', params)

    playlists.value = response.data.data
    totalPages.value = Math.ceil(response.data.total / pageSize)
  } catch (error) {
    handleError(error, '获取歌单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = useDebounceFn(() => {
  currentPage.value = 1
  fetchPlaylists()
}, 300)

// 排序处理
const handleSort = () => {
  currentPage.value = 1
  fetchPlaylists()
}

// 切换排序顺序
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  handleSort()
}

// 分页处理
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    fetchPlaylists()
  }
}

// 编辑歌单
const handleEdit = (playlist: Playlist) => {
  editingPlaylist.value = playlist
  showEditModal.value = true
}

// 删除歌单
const handleDelete = async (playlist: Playlist) => {
  if (!confirm(`确定要删除歌单"${playlist.name}"吗？此操作不可撤销。`)) {
    return
  }

  try {
    const { deletePlaylist } = usePlaylistApi()
    await deletePlaylist(playlist.id)

    showNotification('歌单删除成功', 'success')
    await fetchPlaylists()
  } catch (error) {
    handleError(error, '删除歌单失败')
  }
}

// 播放歌单
const handlePlay = (playlist: Playlist) => {
  // TODO: 集成播放器功能
  showNotification(`开始播放歌单: ${playlist.name}`, 'info')
}

// 创建成功处理
const handleCreateSuccess = () => {
  showCreateModal.value = false
  fetchPlaylists()
}

// 编辑成功处理
const handleEditSuccess = () => {
  showEditModal.value = false
  editingPlaylist.value = null
  fetchPlaylists()
}

// 页面加载时获取数据
onMounted(() => {
  fetchPlaylists()
})
</script>

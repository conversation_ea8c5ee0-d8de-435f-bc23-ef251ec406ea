<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/public" class="flex items-center justify-center space-x-2 mb-6">
          <Icon name="MusicalNoteIcon" class="w-10 h-10 text-primary-500" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">
            MusicDou
          </span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">
          欢迎回来
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          登录到你的账户
        </p>
      </div>

      <!-- Login Form -->
      <Card class="mt-8">
        <form class="space-y-6" @submit.prevent="handleLogin">
          <!-- Email -->
          <Input
            v-model="form.email"
            type="email"
            label="邮箱地址"
            placeholder="请输入邮箱地址"
            left-icon="EnvelopeIcon"
            required
            :error="errors.email"
            @blur="validateEmail"
          />

          <!-- Password -->
          <Input
            v-model="form.password"
            type="password"
            label="密码"
            placeholder="请输入密码"
            left-icon="LockClosedIcon"
            required
            :error="errors.password"
            @blur="validatePassword"
          />

          <!-- Remember Me -->
          <div class="flex items-center justify-between">
            <label class="flex items-center">
              <input
                v-model="form.rememberMe"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                记住我
              </span>
            </label>
            <NuxtLink
              to="/forgot-password"
              class="text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400"
            >
              忘记密码？
            </NuxtLink>
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            :loading="isLoading"
            :disabled="!isFormValid"
            full-width
            size="lg"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </Button>

          <!-- Error Message -->
          <div v-if="loginError" class="text-center">
            <p class="text-sm text-red-600 dark:text-red-400">
              {{ loginError }}
            </p>
          </div>
        </form>
      </Card>

      <!-- Social Login -->
      <Card class="mt-6">
        <div class="space-y-4">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                或者使用
              </span>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              @click="handleSocialLogin('google')"
            >
              <Icon name="google" class="w-5 h-5 mr-2" />
              Google
            </Button>
            <Button
              variant="outline"
              @click="handleSocialLogin('github')"
            >
              <Icon name="github" class="w-5 h-5 mr-2" />
              GitHub
            </Button>
          </div>
        </div>
      </Card>

      <!-- Register Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          还没有账户？
          <NuxtLink
            to="/register"
            class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400"
          >
            立即注册
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '登录',
  layout: false,
  middleware: 'guest'
})

// 表单数据
const form = reactive({
  email: '',
  password: '',
  rememberMe: false
})

// 错误状态
const errors = reactive({
  email: '',
  password: ''
})

// 加载状态
const isLoading = ref(false)
const loginError = ref('')

// 认证相关
const { login } = useAuth()
const router = useRouter()
const route = useRoute()

// 表单验证
const validateEmail = () => {
  if (!form.email) {
    errors.email = '请输入邮箱地址'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = '请输入有效的邮箱地址'
  } else {
    errors.email = ''
  }
}

const validatePassword = () => {
  if (!form.password) {
    errors.password = '请输入密码'
  } else if (form.password.length < 6) {
    errors.password = '密码至少需要6个字符'
  } else {
    errors.password = ''
  }
}

// 表单有效性
const isFormValid = computed(() => {
  return form.email && 
         form.password && 
         !errors.email && 
         !errors.password
})

// 登录处理
const handleLogin = async () => {
  // 验证表单
  validateEmail()
  validatePassword()
  
  if (!isFormValid.value) {
    return
  }

  isLoading.value = true
  loginError.value = ''

  try {
    await login({
      email: form.email,
      password: form.password,
      rememberMe: form.rememberMe
    })

    // 登录成功，重定向
    const redirectTo = route.query.redirect as string || '/'
    await router.push(redirectTo)
  } catch (error: any) {
    loginError.value = error.message || '登录失败，请检查邮箱和密码'
  } finally {
    isLoading.value = false
  }
}

// 社交登录
const handleSocialLogin = async (provider: string) => {
  try {
    // 这里实现社交登录逻辑
    console.log(`Social login with ${provider}`)
  } catch (error: any) {
    loginError.value = error.message || `${provider} 登录失败`
  }
}

// 页面标题
useHead({
  title: '登录 - MusicDou',
  meta: [
    { name: 'description', content: '登录到 MusicDou，开始你的音乐之旅' }
  ]
})
</script>

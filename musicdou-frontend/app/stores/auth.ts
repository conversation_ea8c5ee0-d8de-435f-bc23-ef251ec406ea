import { defineStore } from 'pinia'
import type { User, LoginForm, RegisterForm } from '~/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const isLoggedIn = computed(() => !!user.value)
  const isLoading = ref(false)

  // API实例
  const { get, post } = useApi()

  // 登录
  const login = async (credentials: LoginForm) => {
    try {
      isLoading.value = true
      const response = await post('/auth/login', credentials)
      
      if (response.success) {
        // 保存token
        const token = useCookie('auth-token', {
          default: () => null,
          maxAge: 60 * 60 * 24 * 7 // 7天
        })
        token.value = response.data.token
        
        // 保存用户信息
        user.value = response.data.user
        
        return response
      }
      
      throw new Error(response.message || '登录失败')
    } catch (error) {
      console.error('登录错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterForm) => {
    try {
      isLoading.value = true
      const response = await post('/auth/register', userData)
      
      if (response.success) {
        return response
      }
      
      throw new Error(response.message || '注册失败')
    } catch (error) {
      console.error('注册错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await post('/auth/logout')
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      // 清除本地数据
      const token = useCookie('auth-token')
      token.value = null
      user.value = null
      
      // 跳转到首页
      await navigateTo('/')
    }
  }

  // 获取当前用户信息
  const fetchUser = async () => {
    try {
      const token = useCookie('auth-token')
      if (!token.value) return
      
      const response = await get('/auth/me')
      if (response.success) {
        user.value = response.data
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      // 如果token无效，清除本地数据
      const token = useCookie('auth-token')
      token.value = null
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      const response = await post('/auth/profile', profileData)
      
      if (response.success) {
        user.value = { ...user.value, ...response.data }
        return response
      }
      
      throw new Error(response.message || '更新失败')
    } catch (error) {
      console.error('更新用户信息错误:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    const token = useCookie('auth-token')
    if (token.value) {
      await fetchUser()
    }
  }

  return {
    // 状态
    user: readonly(user),
    isLoggedIn,
    isLoading: readonly(isLoading),
    
    // 方法
    login,
    register,
    logout,
    fetchUser,
    updateProfile,
    initAuth
  }
})

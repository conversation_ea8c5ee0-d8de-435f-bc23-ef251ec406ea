export default defineNuxtRouteMiddleware((to) => {
  const { checkAuth, initAuth } = useAuth()
  
  // 在客户端检查认证状态
  if (process.client) {
    // 如果用户未登录，重定向到登录页面
    if (!checkAuth()) {
      return navigateTo({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  }
  
  // 在服务端，我们需要检查cookie中的token
  if (process.server) {
    const token = useCookie('auth-token')
    if (!token.value) {
      return navigateTo({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  }
})

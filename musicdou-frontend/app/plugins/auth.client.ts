export default defineNuxtPlugin(async () => {
  const { initAuth, refreshToken } = useAuth()
  
  // 初始化认证状态
  await initAuth()
  
  // 设置token自动刷新
  const token = useCookie('auth-token')
  if (token.value) {
    // 每30分钟尝试刷新token
    setInterval(async () => {
      try {
        await refreshToken()
      } catch (error) {
        console.warn('Token刷新失败:', error)
      }
    }, 30 * 60 * 1000) // 30分钟
  }
})
